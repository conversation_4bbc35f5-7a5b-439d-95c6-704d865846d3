import { NativeModules, Platform } from 'react-native';

const { NotificationPermissionModule } = NativeModules;

export interface NotificationPermissionStatus {
  enabled: boolean;
  status: 'granted' | 'denied' | 'unknown';
}

export interface ChannelSettings {
  channelId: string;
  enabled: boolean;
}

class NotificationPermissionService {
  /**
   * Check if notifications are enabled for the app
   */
  async areNotificationsEnabled(): Promise<NotificationPermissionStatus> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.areNotificationsEnabled();
        return result;
      }
      
      // For iOS or if module is not available, assume enabled
      return { enabled: true, status: 'granted' };
    } catch (error) {
      console.error('Error checking notification permission:', error);
      return { enabled: false, status: 'unknown' };
    }
  }

  /**
   * Open the device notification settings for this app
   */
  async openNotificationSettings(): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        await NotificationPermissionModule.openNotificationSettings();
        return true;
      }
      
      // For iOS, use the standard Linking approach
      const { Linking } = require('react-native');
      await Linking.openURL('app-settings:');
      return true;
    } catch (error) {
      console.error('Error opening notification settings:', error);
      return false;
    }
  }

  /**
   * Get settings for a specific notification channel
   */
  async getChannelSettings(channelId: string): Promise<ChannelSettings | null> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.getChannelSettings(channelId);
        return result;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting channel settings:', error);
      return null;
    }
  }

  /**
   * Request notification permission (mainly for Android 13+)
   */
  async requestNotificationPermission(): Promise<NotificationPermissionStatus> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const result = await NotificationPermissionModule.requestNotificationPermission();
        return result;
      }
      
      return { enabled: true, status: 'granted' };
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return { enabled: false, status: 'unknown' };
    }
  }

  /**
   * Create a test notification to verify the system is working
   */
  async createTestNotification(title: string = 'UEST Test', body: string = 'Test notification from UEST app'): Promise<number | null> {
    try {
      if (Platform.OS === 'android' && NotificationPermissionModule) {
        const notificationId = await NotificationPermissionModule.createTestNotification(title, body);
        return notificationId;
      }
      
      return null;
    } catch (error) {
      console.error('Error creating test notification:', error);
      throw error;
    }
  }

  /**
   * Check if the app can send notifications and provide detailed status
   */
  async getDetailedNotificationStatus(): Promise<{
    appNotificationsEnabled: boolean;
    channelStatuses: { [key: string]: boolean };
    canSendNotifications: boolean;
    recommendedAction?: string;
  }> {
    try {
      const appStatus = await this.areNotificationsEnabled();
      const channelStatuses: { [key: string]: boolean } = {};
      
      // Check main channels
      const channels = ['classwork-channel-v3', 'quiz-channel', 'urgent-channel'];
      
      for (const channelId of channels) {
        const channelStatus = await this.getChannelSettings(channelId);
        channelStatuses[channelId] = channelStatus?.enabled ?? false;
      }
      
      const canSendNotifications = appStatus.enabled && Object.values(channelStatuses).some(enabled => enabled);
      
      let recommendedAction: string | undefined;
      if (!appStatus.enabled) {
        recommendedAction = 'Enable notifications in app settings';
      } else if (!canSendNotifications) {
        recommendedAction = 'Enable notification channels in app settings';
      }
      
      return {
        appNotificationsEnabled: appStatus.enabled,
        channelStatuses,
        canSendNotifications,
        recommendedAction,
      };
    } catch (error) {
      console.error('Error getting detailed notification status:', error);
      return {
        appNotificationsEnabled: false,
        channelStatuses: {},
        canSendNotifications: false,
        recommendedAction: 'Check notification settings manually',
      };
    }
  }
}

export default new NotificationPermissionService();
