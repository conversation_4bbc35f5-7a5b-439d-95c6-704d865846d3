import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { openDeviceSettings } from '../Utils/NotificationUtils';
import { PrimaryColors } from '../Utils/Constants';

interface NotificationPermissionDialogProps {
  visible: boolean;
  onCancel: () => void;
  onOpenSettings: () => void;
}

const NotificationPermissionDialog: React.FC<NotificationPermissionDialogProps> = ({
  visible,
  onCancel,
  onOpenSettings,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <Ionicons
              name="notifications"
              size={40}
              color="#FD904B"
            />
          </View>
          
          <Text style={styles.title}>Notification Permission Required</Text>
          
          <Text style={styles.message}>
            To receive important updates about your classes and quizzes, please enable 
            notifications in your device settings.
          </Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onCancel}
            >
              <Text style={styles.cancelButtonText}>CANCEL</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.settingsButton]}
              onPress={() => {
                onOpenSettings();
                openDeviceSettings();
              }}
            >
              <Text style={styles.settingsButtonText}>OPEN SETTINGS</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },

  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#555555',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    minWidth: '45%',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  settingsButton: {
    backgroundColor: '#FD904B',
  },
  cancelButtonText: {
    color: '#555555',
    fontWeight: '600',
    fontSize: 14,
  },
  settingsButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default NotificationPermissionDialog;
