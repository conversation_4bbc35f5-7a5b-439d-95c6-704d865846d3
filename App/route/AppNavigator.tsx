import React, {useEffect, useState} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import UseAppAs from '../Screens/UseAppAs/UseAppAs.tsx';
import Login from '../Screens/Student/Login/Login.tsx';
import Registration from '../Screens/Student/Registration/Registration.tsx';
import TabNavigator from './TabNavigator.tsx';
import ClassList from '../Screens/Student/ClassList/ClassList.tsx';
import Profile from '../Screens/Student/Profile/Profile.tsx';
import ViewResult from '../Screens/Student/ViewResult/ViewResult.tsx';
import TutorLogin from '../Screens/Tutor/Login/TutorLogin.tsx';
import ForgotPassword from '../Screens/Tutor/ForgotPassword/ForgotPassword.tsx';
import RegisterTutor from '../Screens/Tutor/RegisterTutor/RegisterTutor.tsx';
import TutorTabNavigator from '../../App/route/TutorTabNavigator.tsx';
import About from '../Screens/Tutor/Profile/About/About.tsx';
import Description from '../Screens/Tutor/Profile/Description/Description.tsx';
import PhotoAndLogo from '../Screens/Tutor/Profile/PhotoAndLogo/PhotoAndLogo.tsx';
import Education from '../Screens/Tutor/Profile/Education/Education.tsx';
import Experience from '../Screens/Tutor/Profile/Experience/Experience.tsx';
import Certificate from '../Screens/Tutor/Profile/Certificate/Certificate.tsx';
import TutionClass from '../Screens/Tutor/Profile/TutionClass/TutionClass.tsx';
import CreateTution from '../Screens/Tutor/CreateTution/CreateTution.tsx';
import Blog from '../Screens/Tutor/Blog/Blog.tsx';
import Testmonials from '../Screens/Tutor/Testimonials/Testmonials.tsx';
import Thoughts from '../Screens/Tutor/Thoughts/Thoughts.tsx';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ActivityIndicator, View} from 'react-native';
import Review from '../Screens/Student/Profile/Review/Review.tsx';
import Wishlist from '../Screens/Student/Wishlist/Wishlist.tsx';
import StudentProfile from '../Screens/Student/StudentProfile/StudentProfile.tsx';
import Payment from '../Screens/Student/Payment/Payment.tsx';
import Referral from '../Screens/Student/Referral/Referral.tsx';
import Search from '../Screens/Student/Search/Search.tsx';
import AddCoins from '../Screens/Student/Payment/AddCoins.tsx';
import WelcomeScreen from '../Screens/Student/Login/WelcomeScreen.tsx';
import SignUpScreen from '../Screens/Student/Registration/SignUpScreen';
import OTPScreen from '../Screens/Student/Registration/OTPScreen';
import DailyQuiz from '../Screens/Student/DailyQuiz/DailyQuiz.tsx';
import Leaderboard from '../Screens/Student/DailyQuiz/Leaderboard.tsx';
import DailyResults from '../Screens/Student/DailyQuiz/DailyResults.tsx';
import Notifications from '../Screens/Student/Notifications/Notifications.tsx';
import Setting from '../Screens/Student/Setting/Setting.tsx';
import NotificationTest from '../TestComponents/NotificationTest.tsx';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  const [initialRoute, setInitialRoute] = useState<string | null>(null);

  useEffect(() => {
    const checkToken = async () => {
      try {
        const token = await AsyncStorage.getItem('token');
        if (token) {
          setInitialRoute('Home');
        } else {
          setInitialRoute('WelcomeScreen');
        }
      } catch (error) {
        console.error('Token check error:', error);
        setInitialRoute('login');
      }
    };
    checkToken();
  }, []);

  // Show loading while checking token
  if (!initialRoute) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" color="blue" />
      </View>
    );
  }

  return (
    <Stack.Navigator
      initialRouteName={initialRoute}
      screenOptions={{headerShown: false}}>
      <Stack.Screen name="UseAppAs" component={UseAppAs} />
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="Home" component={TabNavigator} />
      <Stack.Screen name="Payment" component={Payment} />
      <Stack.Screen name="Registration" component={Registration} />
      <Stack.Screen name="SignUp" component={SignUpScreen} />
      <Stack.Screen name="OTP" component={OTPScreen} />
      <Stack.Screen name="ClassList" component={ClassList} />
      <Stack.Screen name="TutorProfile" component={Profile} />
      <Stack.Screen name="Wishlist" component={Wishlist} />
      <Stack.Screen name="StudentProfile" component={StudentProfile} />
      <Stack.Screen name="ViewResult" component={ViewResult} />
      <Stack.Screen name="TutorLogin" component={TutorLogin} />
      <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
      <Stack.Screen name="RegisterTutor" component={RegisterTutor} />
      <Stack.Screen name="TutorHome" component={TutorTabNavigator} />
      <Stack.Screen name="About" component={About} />
      <Stack.Screen name="Description" component={Description} />
      <Stack.Screen name="PhotoAndLogo" component={PhotoAndLogo} />
      <Stack.Screen name="Education" component={Education} />
      <Stack.Screen name="Experience" component={Experience} />
      <Stack.Screen name="Certificate" component={Certificate} />
      <Stack.Screen name="TutionClass" component={TutionClass} />
      <Stack.Screen name="CreateTution" component={CreateTution} />
      <Stack.Screen name="Blog" component={Blog} />
      <Stack.Screen name="Testmonials" component={Testmonials} />
      <Stack.Screen name="Thoughts" component={Thoughts} />
      <Stack.Screen name="Review" component={Review} />
      <Stack.Screen name="Referral" component={Referral} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="AddCoins" component={AddCoins} />
      <Stack.Screen name="DailyQuiz" component={DailyQuiz} />
      <Stack.Screen name="WelcomeScreen" component={WelcomeScreen} />
      <Stack.Screen name="DailyQuizLeaderboard" component={Leaderboard} />
      <Stack.Screen name="DailyResults" component={DailyResults} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="Setting" component={Setting} />
      <Stack.Screen name="NotificationTest" component={NotificationTest} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
