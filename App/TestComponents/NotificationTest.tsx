import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { setupNotifications, scheduleLocalNotification } from '../NotificationHelper/NotificationService';
import { useNotificationPermission } from '../hooks/useNotificationPermission';
import NotificationPermissionDialog from '../CommonComponents/NotificationPermissionDialog';

const NotificationTest = () => {
  const [status, setStatus] = useState('Ready to test');
  
  const {
    showPermissionDialog,
    isRequestingPermission,
    requestNotificationPermission,
    handleDialogCancel,
    handleDialogOpenSettings,
  } = useNotificationPermission();

  const testNotificationPermission = async () => {
    setStatus('Testing notification permission...');
    
    try {
      const granted = await requestNotificationPermission();
      if (granted) {
        setStatus('✅ Notification permission granted!');
        Alert.alert('Success', 'Notifications are now enabled!');
      } else {
        setStatus('❌ Notification permission denied');
      }
    } catch (error) {
      setStatus(`❌ Error: ${error.message}`);
    }
  };

  const testLocalNotification = async () => {
    setStatus('Sending test notification...');
    
    try {
      // Setup notifications first
      const setupSuccess = await setupNotifications(false);
      
      if (setupSuccess) {
        // Schedule a test notification in 3 seconds
        scheduleLocalNotification(
          'UEST Test Notification',
          'This is a test notification from UEST app!',
          new Date(Date.now() + 3000)
        );
        setStatus('✅ Test notification scheduled for 3 seconds!');
        Alert.alert('Test Scheduled', 'You should receive a notification in 3 seconds');
      } else {
        setStatus('❌ Failed to setup notifications');
        Alert.alert('Error', 'Please enable notifications first');
      }
    } catch (error) {
      setStatus(`❌ Error: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notification System Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status:</Text>
        <Text style={styles.statusText}>{status}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={testNotificationPermission}
          disabled={isRequestingPermission}
        >
          <Text style={styles.buttonText}>
            {isRequestingPermission ? 'Requesting...' : 'Test Permission'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testLocalNotification}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            Send Test Notification
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Platform Info:</Text>
        <Text style={styles.infoText}>OS: {Platform.OS}</Text>
        <Text style={styles.infoText}>Version: {Platform.Version}</Text>
        <Text style={styles.infoText}>
          Permission Required: {Platform.OS === 'android' && Platform.Version >= 33 ? 'Yes' : 'No'}
        </Text>
      </View>

      {/* Notification Permission Dialog */}
      <NotificationPermissionDialog
        visible={showPermissionDialog}
        onCancel={handleDialogCancel}
        onOpenSettings={handleDialogOpenSettings}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#FD904B',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#FD904B',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButtonText: {
    color: '#FD904B',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
});

export default NotificationTest;
