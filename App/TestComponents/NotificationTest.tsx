import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { setupNotifications, scheduleLocalNotification } from '../NotificationHelper/NotificationService';
import { useNotificationPermission } from '../hooks/useNotificationPermission';
import NotificationPermissionDialog from '../CommonComponents/NotificationPermissionDialog';
import NotificationPermissionService from '../services/NotificationPermissionService';

const NotificationTest = () => {
  const [status, setStatus] = useState('Ready to test');
  const [detailedStatus, setDetailedStatus] = useState<any>(null);
  
  const {
    showPermissionDialog,
    isRequestingPermission,
    requestNotificationPermission,
    handleDialogCancel,
    handleDialogOpenSettings,
  } = useNotificationPermission();

  const testNotificationPermission = async () => {
    setStatus('Testing notification permission...');
    
    try {
      const granted = await requestNotificationPermission();
      if (granted) {
        setStatus('✅ Notification permission granted!');
        Alert.alert('Success', 'Notifications are now enabled!');
      } else {
        setStatus('❌ Notification permission denied');
      }
    } catch (error) {
      setStatus(`❌ Error: ${error.message}`);
    }
  };

  const testLocalNotification = async () => {
    setStatus('Sending test notification...');

    try {
      // Use native module to create test notification
      const notificationId = await NotificationPermissionService.createTestNotification(
        'UEST Test Notification',
        'This is a test notification from UEST app!'
      );

      if (notificationId) {
        setStatus(`✅ Test notification sent! ID: ${notificationId}`);
        Alert.alert('Test Sent', 'Check your notification panel!');
      } else {
        setStatus('❌ Failed to send test notification');
        Alert.alert('Error', 'Please enable notifications first');
      }
    } catch (error: any) {
      setStatus(`❌ Error: ${error.message}`);
      if (error.message.includes('PERMISSION_DENIED')) {
        Alert.alert('Permission Denied', 'Please enable notifications in settings first');
      }
    }
  };

  const checkDetailedStatus = async () => {
    setStatus('Checking detailed notification status...');

    try {
      const detailed = await NotificationPermissionService.getDetailedNotificationStatus();
      setDetailedStatus(detailed);
      setStatus('✅ Detailed status loaded');
    } catch (error: any) {
      setStatus(`❌ Error: ${error.message}`);
    }
  };

  const openSettings = async () => {
    setStatus('Opening notification settings...');

    try {
      const success = await NotificationPermissionService.openNotificationSettings();
      if (success) {
        setStatus('✅ Settings opened');
      } else {
        setStatus('❌ Failed to open settings');
      }
    } catch (error: any) {
      setStatus(`❌ Error: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notification System Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status:</Text>
        <Text style={styles.statusText}>{status}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={testNotificationPermission}
          disabled={isRequestingPermission}
        >
          <Text style={styles.buttonText}>
            {isRequestingPermission ? 'Requesting...' : 'Test Permission'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testLocalNotification}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            Send Test Notification
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.tertiaryButton]}
          onPress={checkDetailedStatus}
        >
          <Text style={[styles.buttonText, styles.tertiaryButtonText]}>
            Check Detailed Status
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.settingsButton]}
          onPress={openSettings}
        >
          <Text style={styles.buttonText}>
            Open Notification Settings
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Platform Info:</Text>
        <Text style={styles.infoText}>OS: {Platform.OS}</Text>
        <Text style={styles.infoText}>Version: {Platform.Version}</Text>
        <Text style={styles.infoText}>
          Permission Required: {Platform.OS === 'android' && Platform.Version >= 33 ? 'Yes' : 'No'}
        </Text>
      </View>

      {detailedStatus && (
        <View style={styles.detailedStatusContainer}>
          <Text style={styles.infoTitle}>Detailed Status:</Text>
          <Text style={styles.infoText}>
            App Notifications: {detailedStatus.appNotificationsEnabled ? '✅ Enabled' : '❌ Disabled'}
          </Text>
          <Text style={styles.infoText}>
            Can Send: {detailedStatus.canSendNotifications ? '✅ Yes' : '❌ No'}
          </Text>
          {detailedStatus.recommendedAction && (
            <Text style={styles.recommendationText}>
              💡 {detailedStatus.recommendedAction}
            </Text>
          )}
          <Text style={styles.channelTitle}>Channel Status:</Text>
          {Object.entries(detailedStatus.channelStatuses).map(([channelId, enabled]: [string, any]) => (
            <Text key={channelId} style={styles.channelText}>
              {channelId}: {enabled ? '✅' : '❌'}
            </Text>
          ))}
        </View>
      )}

      {/* Notification Permission Dialog */}
      <NotificationPermissionDialog
        visible={showPermissionDialog}
        onCancel={handleDialogCancel}
        onOpenSettings={handleDialogOpenSettings}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#FD904B',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#FD904B',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButtonText: {
    color: '#FD904B',
  },
  tertiaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#2196F3',
  },
  tertiaryButtonText: {
    color: '#2196F3',
  },
  settingsButton: {
    backgroundColor: '#4CAF50',
  },
  detailedStatusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  channelTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginTop: 10,
    marginBottom: 5,
  },
  channelText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 2,
    fontFamily: 'monospace',
  },
  recommendationText: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '500',
    marginTop: 5,
    marginBottom: 10,
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
});

export default NotificationTest;
