import { useState, useCallback } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';
import { setupNotifications } from '../NotificationHelper/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useNotificationPermission = () => {
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  // Check if notifications are enabled
  const checkNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (Platform.OS === 'android' && Platform.Version >= 33) {
      try {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        return granted;
      } catch (error) {
        console.error('Error checking notification permission:', error);
        return false;
      }
    }
    // For iOS and Android < 13, assume permissions are granted
    return true;
  }, []);

  // Request notification permission with dialog
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (isRequestingPermission) {
      return false;
    }

    setIsRequestingPermission(true);

    try {
      // First check if we already have permission
      const hasPermission = await checkNotificationPermission();
      if (hasPermission) {
        setIsRequestingPermission(false);
        return true;
      }

      // Try to setup notifications
      const setupSuccess = await setupNotifications(false);
      
      if (setupSuccess) {
        // Permission granted
        await AsyncStorage.setItem('notificationsEnabled', JSON.stringify(true));
        setIsRequestingPermission(false);
        return true;
      } else {
        // Permission denied, show dialog
        setShowPermissionDialog(true);
        setIsRequestingPermission(false);
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setIsRequestingPermission(false);
      return false;
    }
  }, [isRequestingPermission, checkNotificationPermission]);

  // Handle dialog actions
  const handleDialogCancel = useCallback(() => {
    setShowPermissionDialog(false);
  }, []);

  const handleDialogOpenSettings = useCallback(() => {
    setShowPermissionDialog(false);
    // The dialog component will handle opening settings
  }, []);

  return {
    showPermissionDialog,
    isRequestingPermission,
    checkNotificationPermission,
    requestNotificationPermission,
    handleDialogCancel,
    handleDialogOpenSettings,
  };
};
