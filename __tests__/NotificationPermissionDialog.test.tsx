import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import NotificationPermissionDialog from '../App/CommonComponents/NotificationPermissionDialog';

// Mock the openDeviceSettings function
jest.mock('../App/Utils/NotificationUtils', () => ({
  openDeviceSettings: jest.fn(),
}));

describe('NotificationPermissionDialog', () => {
  it('renders correctly when visible', () => {
    const { getByText } = render(
      <NotificationPermissionDialog
        visible={true}
        onCancel={jest.fn()}
        onOpenSettings={jest.fn()}
      />
    );

    // Check if the dialog content is rendered
    expect(getByText('Notification Permission Required')).toBeTruthy();
    expect(getByText(/To receive important updates/)).toBeTruthy();
    expect(getByText('CANCEL')).toBeTruthy();
    expect(getByText('OPEN SETTINGS')).toBeTruthy();
  });

  it('calls onCancel when Cancel button is pressed', () => {
    const onCancelMock = jest.fn();
    const { getByText } = render(
      <NotificationPermissionDialog
        visible={true}
        onCancel={onCancelMock}
        onOpenSettings={jest.fn()}
      />
    );

    // Press the Cancel button
    fireEvent.press(getByText('CANCEL'));
    
    // Check if onCancel was called
    expect(onCancelMock).toHaveBeenCalledTimes(1);
  });

  it('calls onOpenSettings when Open Settings button is pressed', () => {
    const onOpenSettingsMock = jest.fn();
    const { getByText } = render(
      <NotificationPermissionDialog
        visible={true}
        onCancel={jest.fn()}
        onOpenSettings={onOpenSettingsMock}
      />
    );

    // Press the Open Settings button
    fireEvent.press(getByText('OPEN SETTINGS'));
    
    // Check if onOpenSettings was called
    expect(onOpenSettingsMock).toHaveBeenCalledTimes(1);
  });

  it('is not rendered when not visible', () => {
    const { queryByText } = render(
      <NotificationPermissionDialog
        visible={false}
        onCancel={jest.fn()}
        onOpenSettings={jest.fn()}
      />
    );

    // Check that the dialog content is not rendered
    expect(queryByText('Notification Permission Required')).toBeNull();
  });
});
