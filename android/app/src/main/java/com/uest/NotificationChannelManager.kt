package com.uest

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationManagerCompat

object NotificationChannelManager {
    
    const val CHANNEL_ID_GENERAL = "classwork-channel-v3"
    const val CHANNEL_ID_QUIZ = "quiz-channel"
    const val CHANNEL_ID_URGENT = "urgent-channel"
    
    fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // General notifications channel
            val generalChannel = NotificationChannel(
                CHANNEL_ID_GENERAL,
                "General Notifications",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "General app notifications and updates"
                enableVibration(true)
                enableLights(true)
            }
            
            // Quiz notifications channel
            val quizChannel = NotificationChannel(
                CHANNEL_ID_QUIZ,
                "Quiz Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Daily quiz and exam notifications"
                enableVibration(true)
                enableLights(true)
            }
            
            // Urgent notifications channel
            val urgentChannel = NotificationChannel(
                CHANNEL_ID_URGENT,
                "Urgent Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Important and urgent notifications"
                enableVibration(true)
                enableLights(true)
            }
            
            // Create all channels
            notificationManager.createNotificationChannel(generalChannel)
            notificationManager.createNotificationChannel(quizChannel)
            notificationManager.createNotificationChannel(urgentChannel)
            
            android.util.Log.d("NotificationChannels", "All notification channels created successfully")
        }
    }
    
    fun areNotificationsEnabled(context: Context): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
    
    fun isChannelEnabled(context: Context, channelId: String): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val channel = notificationManager.getNotificationChannel(channelId)
            return channel?.importance != NotificationManager.IMPORTANCE_NONE
        }
        return areNotificationsEnabled(context)
    }
}
