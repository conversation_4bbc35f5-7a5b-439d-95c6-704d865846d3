package com.uest

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationManagerCompat

object NotificationChannelManager {
    
    const val CHANNEL_ID_GENERAL = "classwork-channel-v3"
    const val CHANNEL_ID_QUIZ = "quiz-channel"
    const val CHANNEL_ID_URGENT = "urgent-channel"
    
    fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Check if channels already exist to avoid recreation
            if (notificationManager.getNotificationChannel(CHANNEL_ID_GENERAL) != null) {
                android.util.Log.d("NotificationChannels", "Channels already exist, skipping creation")
                return
            }

            // General notifications channel (UEST Classwork)
            val generalChannel = NotificationChannel(
                CHANNEL_ID_GENERAL,
                "UEST Classwork",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for classwork, homework, and school updates"
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }

            // Quiz notifications channel
            val quizChannel = NotificationChannel(
                CHANNEL_ID_QUIZ,
                "UEST Daily Quiz",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Daily quiz notifications and results"
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }

            // Urgent notifications channel
            val urgentChannel = NotificationChannel(
                CHANNEL_ID_URGENT,
                "UEST Important",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Important announcements and urgent notifications"
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }

            // Create all channels
            notificationManager.createNotificationChannel(generalChannel)
            notificationManager.createNotificationChannel(quizChannel)
            notificationManager.createNotificationChannel(urgentChannel)

            android.util.Log.d("NotificationChannels", "All notification channels created successfully")
        }
    }
    
    fun areNotificationsEnabled(context: Context): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
    
    fun isChannelEnabled(context: Context, channelId: String): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val channel = notificationManager.getNotificationChannel(channelId)
            return channel?.importance != NotificationManager.IMPORTANCE_NONE
        }
        return areNotificationsEnabled(context)
    }
}
