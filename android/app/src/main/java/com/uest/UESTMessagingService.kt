package com.uest

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.facebook.react.ReactApplication
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class UESTMessagingService : FirebaseMessagingService() {

    /**
     * Called when message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        // Not getting messages here? See why this may be: https://goo.gl/39bRNJ
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Check if message contains a data payload.
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            
            // Handle the data payload
            val title = remoteMessage.data["title"] ?: "UEST Notification"
            val body = remoteMessage.data["body"] ?: "You have a new notification"
            val channelId = remoteMessage.data["channelId"] ?: "classwork-channel-v3"
            
            sendNotification(title, body, channelId)
        }

        // Check if message contains a notification payload.
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
            sendNotification(it.title ?: "UEST Notification", it.body ?: "You have a new notification", "classwork-channel-v3")
        }
    }

    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")

        // Send token to your app server to associate with user
        // This will be handled by the React Native code in NotificationService.js
        // We just need to make sure the app is notified about the new token
        
        // Get the React instance
        val reactApplication = application as ReactApplication
        val reactInstanceManager = reactApplication.reactNativeHost.reactInstanceManager
        
        // If the React instance is ready, emit an event
        if (reactInstanceManager.hasStartedCreatingInitialContext()) {
            // The React context is already created, we can emit an event
            // This is handled by the React Native module
            Log.d(TAG, "React context already created, token will be handled by RN")
        } else {
            // React context not created yet, we'll store the token and it will be handled
            // when the app starts via the normal onRegister callback
            Log.d(TAG, "React context not created yet, token will be handled on app start")
        }
    }

    /**
     * Create and show a simple notification containing the received FCM message.
     *
     * @param messageTitle FCM message title received.
     * @param messageBody FCM message body received.
     * @param channelId The notification channel ID.
     */
    private fun sendNotification(messageTitle: String, messageBody: String, channelId: String) {
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_notification)  // Use proper notification icon
            .setContentTitle(messageTitle)
            .setContentText(messageBody)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Channels are already created in MainApplication, no need to create again
        // This prevents duplicate channel creation and ensures proper settings

        // Generate unique notification ID to avoid overwriting previous notifications
        val notificationId = System.currentTimeMillis().toInt()
        notificationManager.notify(notificationId, notificationBuilder.build())

        Log.d(TAG, "Notification sent with ID: $notificationId, Channel: $channelId")
    }

    companion object {
        private const val TAG = "UESTMessagingService"
    }
}
